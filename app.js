const express = require('express')
const app = express()
const port = 3000

// Global error handlers for uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message)
  console.error('Stack trace:', error.stack)
  console.error('🔄 Shutting down gracefully...')
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise)
  console.error('Reason:', reason)
  console.error('🔄 Shutting down gracefully...')
  process.exit(1)
})

// Middleware to parse JSON request bodies with error handling
app.use(express.json({
  limit: '10mb',
  verify: (_req, _res, buf, _encoding) => {
    try {
      JSON.parse(buf)
    } catch (error) {
      console.error('❌ Invalid JSON in request body:', error.message)
      throw new Error('Invalid JSON format')
    }
  }
}))

// Global error handling middleware
app.use((error, req, res, _next) => {
  console.error('❌ Express Error Handler:', error.message)
  console.error('Stack trace:', error.stack)
  console.error('Request URL:', req.url)
  console.error('Request method:', req.method)

  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  })
})

// POST endpoint with error handling
app.post('/api/data', (req, res, next) => {
  try {
    console.log('✅ Received POST request to /api/data')
    console.log('Request body:', req.body)
    console.log('Request headers:', req.headers)

    // Simulate potential error handling
    if (!req.body) {
      throw new Error('Request body is required')
    }

    res.status(200).json({
      message: 'OK',
      timestamp: new Date().toISOString(),
      receivedData: req.body
    })
  } catch (error) {
    console.error('❌ Error in /api/data endpoint:', error.message)
    next(error) // Pass error to global error handler
  }
})

// Health check endpoint
app.get('/health', (_req, res) => {
  console.log('✅ Health check requested')
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// 404 handler for undefined routes
app.use('*', (req, res) => {
  console.log(`⚠️  404 - Route not found: ${req.method} ${req.originalUrl}`)
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  })
})

// Start server with error handling
const server = app.listen(port, () => {
  console.log(`🚀 Server successfully started on port ${port}`)
  console.log(`📍 Health check available at: http://localhost:${port}/health`)
  console.log(`📍 API endpoint available at: http://localhost:${port}/api/data`)
  console.log(`🕐 Started at: ${new Date().toISOString()}`)
})

// Handle server startup errors
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use`)
    console.error('💡 Try using a different port or stop the process using this port')
  } else {
    console.error('❌ Server startup error:', error.message)
  }
  process.exit(1)
})

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...')
  server.close(() => {
    console.log('✅ Server closed successfully')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('\n🔄 SIGINT received (Ctrl+C), shutting down gracefully...')
  server.close(() => {
    console.log('✅ Server closed successfully')
    process.exit(0)
  })
})